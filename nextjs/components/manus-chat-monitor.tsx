"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useWebSocket } from '@/contexts/WebSocketContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, ChevronDown, ChevronRight, Copy, Download, Eye, File, FileText, Code, ImageIcon, Archive, Edit3, Loader2, Send, Monitor, Play, Pause, SkipBack, SkipForward, Maximize2, Minimize2, X, Share, MoreHorizontal, Clock, Zap, Brain, Settings, Plus, Moon, Sun, Volume2, VolumeX, Smartphone, Tablet, Laptop } from 'lucide-react';

// Import ChatMessage type from WebSocketContext
import { ChatMessage as WebSocketChatMessage } from '@/contexts/WebSocketContext';

// Enhanced message interface
interface ChatMessage extends WebSocketChatMessage {
  status?: 'sending' | 'sent' | 'error';
  content_type?: 'text' | 'plan' | 'code' | 'file_attachment' | 'completion';
  plan_data?: {
    title?: string;
    steps: Array<{
      title: string;
      description: string;
      status: 'pending' | 'in_progress' | 'completed';
      substeps?: Array<{
        title: string;
        status: 'pending' | 'in_progress' | 'completed';
      }>;
      files?: Array<{
        name: string;
        action: 'reading' | 'creating' | 'modifying';
      }>;
    }>;
  };
  code_data?: {
    language: string;
    code: string;
    filename?: string;
    description?: string;
  };
  file_data?: {
    filename: string;
    size: string;
    type: 'code' | 'document' | 'image' | 'other';
    url?: string;
    description?: string;
  };
  completion_data?: {
    title: string;
    description: string;
    deliverables?: Array<{
      name: string;
      type: string;
      size: string;
    }>;
  };
}

// Enhanced Manus Logo Component
const ManusLogo: React.FC<{ size?: number; className?: string }> = ({ size = 24, className = "" }) => (
  <div className={`flex items-center justify-center ${className}`} style={{ width: size, height: size }}>
    <svg viewBox="0 0 32 32" fill="currentColor" style={{ width: size, height: size }}>
      <path d="M16 4L4 10l12 6 12-6-12-6z" opacity="0.8"/>
      <path d="M4 14l12 6 12-6" opacity="0.6"/>
      <path d="M4 18l12 6 12-6" opacity="0.4"/>
      <path d="M4 22l12 6 12-6" opacity="0.2"/>
    </svg>
  </div>
);

// Enhanced Plan Renderer with better animations and styling
const PlanRenderer: React.FC<{ planData: ChatMessage['plan_data'] }> = ({ planData }) => {
  const [expandedSteps, setExpandedSteps] = useState<Set<number>>(new Set([0]));

  const toggleStep = (stepIndex: number) => {
    const newExpanded = new Set(expandedSteps);
    if (newExpanded.has(stepIndex)) {
      newExpanded.delete(stepIndex);
    } else {
      newExpanded.add(stepIndex);
    }
    setExpandedSteps(newExpanded);
  };

  if (!planData?.steps) return null;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-emerald-400" />;
      case 'in_progress':
        return <Loader2 className="h-4 w-4 text-blue-400 animate-spin" />;
      default:
        return <div className="h-4 w-4 rounded-full border-2 border-gray-600 bg-transparent" />;
    }
  };

  const getFileIcon = (action: string) => {
    switch (action) {
      case 'reading':
        return <Eye className="h-3 w-3 text-blue-400" />;
      case 'creating':
        return <Plus className="h-3 w-3 text-emerald-400" />;
      case 'modifying':
        return <Edit3 className="h-3 w-3 text-amber-400" />;
      default:
        return <File className="h-3 w-3 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-1">
      {planData.title && (
        <div className="mb-4">
          <h3 className="text-base font-medium text-gray-100 mb-1">{planData.title}</h3>
        </div>
      )}

      {planData.steps.map((step, index) => (
        <div key={index} className="group">
          <div
            className="flex items-start gap-3 p-3 rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-800/50"
            onClick={() => toggleStep(index)}
          >
            <div className="flex-shrink-0 mt-0.5">
              {getStatusIcon(step.status)}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="text-sm font-medium text-gray-100 leading-tight">
                  {step.title}
                </h4>
                {expandedSteps.has(index) ? (
                  <ChevronDown className="h-4 w-4 text-gray-400 transition-transform duration-200" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-400 transition-transform duration-200" />
                )}
              </div>
              {step.description && !expandedSteps.has(index) && (
                <p className="text-xs text-gray-400 leading-relaxed">
                  {step.description}
                </p>
              )}
            </div>
          </div>

          {expandedSteps.has(index) && (
            <div className="ml-7 pb-2 space-y-3 animate-in slide-in-from-top-2 duration-200">
              {step.description && (
                <p className="text-sm text-gray-300 leading-relaxed">
                  {step.description}
                </p>
              )}

              {/* File operations */}
              {step.files && step.files.length > 0 && (
                <div className="space-y-2">
                  {step.files.map((file, fileIndex) => (
                    <div key={fileIndex} className="flex items-center gap-3 p-2.5 bg-gray-900/60 rounded-md border border-gray-700/50">
                      <div className="flex items-center gap-2">
                        {getFileIcon(file.action)}
                        <span className="text-xs text-gray-400 capitalize font-medium">{file.action} file</span>
                      </div>
                      <code className="text-xs bg-gray-800/80 px-2 py-1 rounded font-mono text-gray-300 border border-gray-700/50">
                        {file.name}
                      </code>
                    </div>
                  ))}
                </div>
              )}

              {/* Substeps */}
              {step.substeps && step.substeps.length > 0 && (
                <div className="space-y-2 pl-4 border-l border-gray-700/50">
                  {step.substeps.map((substep, subIndex) => (
                    <div key={subIndex} className="flex items-center gap-3">
                      <div className="flex-shrink-0">
                        {getStatusIcon(substep.status)}
                      </div>
                      <span className="text-sm text-gray-300 leading-relaxed">
                        {substep.title}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

// Enhanced Code Renderer with better syntax highlighting
const CodeRenderer: React.FC<{ codeData: ChatMessage['code_data'] }> = ({ codeData }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    if (codeData?.code) {
      await navigator.clipboard.writeText(codeData.code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  if (!codeData?.code) return null;

  const getLanguageColor = (language: string) => {
    switch (language?.toLowerCase()) {
      case 'javascript':
      case 'js':
        return 'text-yellow-400';
      case 'typescript':
      case 'ts':
        return 'text-blue-400';
      case 'python':
      case 'py':
        return 'text-green-400';
      case 'bash':
      case 'shell':
        return 'text-gray-300';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className="bg-gray-950 rounded-lg border border-gray-800 overflow-hidden shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-3 bg-gray-900/80 border-b border-gray-800">
        <div className="flex items-center gap-3">
          <div className="flex gap-1.5">
            <div className="w-3 h-3 rounded-full bg-red-500/80"></div>
            <div className="w-3 h-3 rounded-full bg-yellow-500/80"></div>
            <div className="w-3 h-3 rounded-full bg-green-500/80"></div>
          </div>
          <div className="flex items-center gap-2">
            <Code className="h-4 w-4 text-gray-400" />
            <span className="text-sm font-medium text-gray-300">
              {codeData.filename || 'Code'}
            </span>
            {codeData.language && (
              <Badge variant="outline" className={`text-xs border-gray-700 ${getLanguageColor(codeData.language)}`}>
                {codeData.language.toUpperCase()}
              </Badge>
            )}
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={copyToClipboard}
          className="h-8 w-8 p-0 text-gray-400 hover:text-gray-200 hover:bg-gray-800"
        >
          {copied ? (
            <CheckCircle className="h-4 w-4 text-emerald-400" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Description */}
      {codeData.description && (
        <div className="px-4 py-2 bg-gray-900/40 border-b border-gray-800">
          <p className="text-xs text-gray-400">{codeData.description}</p>
        </div>
      )}

      {/* Code Content */}
      <div className="p-4 overflow-x-auto max-h-96">
        <pre className="text-sm text-gray-100 font-mono leading-relaxed">
          <code className="language-{codeData.language}">{codeData.code}</code>
        </pre>
      </div>

      {/* Copy feedback */}
      {copied && (
        <div className="absolute top-2 right-2 bg-emerald-500/90 text-white text-xs px-2 py-1 rounded-md animate-in fade-in-0 duration-200">
          Copied!
        </div>
      )}
    </div>
  );
};

// Enhanced File Attachment Renderer
const FileAttachmentRenderer: React.FC<{ fileData: ChatMessage['file_data'] }> = ({ fileData }) => {
  if (!fileData) return null;

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'code':
        return <Code className="h-5 w-5 text-blue-400" />;
      case 'document':
        return <FileText className="h-5 w-5 text-emerald-400" />;
      case 'image':
        return <ImageIcon className="h-5 w-5 text-purple-400" />;
      default:
        return <Archive className="h-5 w-5 text-gray-400" />;
    }
  };

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'code':
        return 'border-blue-500/30 bg-blue-500/5';
      case 'document':
        return 'border-emerald-500/30 bg-emerald-500/5';
      case 'image':
        return 'border-purple-500/30 bg-purple-500/5';
      default:
        return 'border-gray-500/30 bg-gray-500/5';
    }
  };

  return (
    <div className={`flex items-center gap-4 p-4 rounded-lg border transition-all duration-200 hover:bg-gray-800/30 ${getFileTypeColor(fileData.type)}`}>
      <div className="flex-shrink-0">
        {getFileIcon(fileData.type)}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="text-sm font-medium text-gray-100 truncate">
            {fileData.filename}
          </h4>
          <Badge variant="outline" className="text-xs border-gray-700 text-gray-400 capitalize">
            {fileData.type}
          </Badge>
        </div>
        <div className="flex items-center gap-3 text-xs text-gray-400">
          <span>{fileData.size}</span>
          {fileData.description && (
            <>
              <span>•</span>
              <span>{fileData.description}</span>
            </>
          )}
        </div>
      </div>
      <div className="flex-shrink-0 flex gap-1">
        {fileData.url && (
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-400 hover:text-gray-200">
            <Eye className="h-4 w-4" />
          </Button>
        )}
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-400 hover:text-gray-200">
          <Download className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

// Completion Status Renderer
const CompletionRenderer: React.FC<{ completionData: ChatMessage['completion_data'] }> = ({ completionData }) => {
  if (!completionData) return null;

  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3 p-4 bg-emerald-500/10 border border-emerald-500/30 rounded-lg">
        <CheckCircle className="h-5 w-5 text-emerald-400 mt-0.5" />
        <div className="flex-1">
          <h4 className="text-sm font-medium text-emerald-400 mb-1">
            {completionData.title}
          </h4>
          <p className="text-sm text-gray-300 leading-relaxed">
            {completionData.description}
          </p>
        </div>
      </div>

      {completionData.deliverables && completionData.deliverables.length > 0 && (
        <div className="space-y-2">
          <h5 className="text-xs font-medium text-gray-400 uppercase tracking-wide">Deliverables</h5>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {completionData.deliverables.map((deliverable, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg border border-gray-700/50">
                <FileText className="h-4 w-4 text-gray-400" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-200 truncate">
                    {deliverable.name}
                  </div>
                  <div className="text-xs text-gray-400">
                    {deliverable.type} • {deliverable.size}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Enhanced Manus Computer Component
const ManusComputer: React.FC<{
  isVisible: boolean;
  onToggle: () => void;
  isMinimized: boolean;
  onMinimizeToggle: () => void;
}> = ({ isVisible, onToggle, isMinimized, onMinimizeToggle }) => {
  const [isPlaying, setIsPlaying] = useState(true);
  const [currentTab, setCurrentTab] = useState('Modified');

  if (!isVisible) return null;

  return (
    <div className={`fixed bottom-6 right-6 bg-gray-900 border border-gray-700 rounded-xl shadow-2xl transition-all duration-300 ${
      isMinimized ? 'w-80 h-16' : 'w-[600px] h-[500px]'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="flex items-center gap-3">
          <Monitor className="h-5 w-5 text-gray-400" />
          <span className="text-sm font-medium text-gray-100">Manus's Computer</span>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-gray-400">live</span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-gray-200">
            <Share className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onMinimizeToggle}
            className="h-6 w-6 p-0 text-gray-400 hover:text-gray-200"
          >
            {isMinimized ? <Maximize2 className="h-3 w-3" /> : <Minimize2 className="h-3 w-3" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="h-6 w-6 p-0 text-gray-400 hover:text-gray-200"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Status */}
          <div className="p-4 border-b border-gray-700 bg-gray-800/50">
            <div className="flex items-center gap-2 text-xs text-gray-400 mb-2">
              <Edit3 className="h-3 w-3" />
              <span>Manus is using Editor</span>
            </div>
            <div className="text-sm text-gray-300">
              Creating file <code className="bg-gray-800 px-2 py-1 rounded text-xs font-mono">validation_report_refactored.md</code>
            </div>
          </div>

          {/* Tabs */}
          <div className="flex items-center border-b border-gray-700 bg-gray-800/30">
            {['Diff', 'Original', 'Modified'].map((tab) => (
              <button
                key={tab}
                onClick={() => setCurrentTab(tab)}
                className={`px-4 py-2 text-xs font-medium transition-colors ${
                  currentTab === tab
                    ? 'text-gray-100 border-b-2 border-blue-500 bg-gray-800/50'
                    : 'text-gray-400 hover:text-gray-300'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Editor Content */}
          <div className="flex-1 bg-gray-950 relative overflow-hidden">
            <div className="absolute inset-0 p-4">
              <div className="h-full bg-gray-900 rounded border border-gray-800 p-4 font-mono text-xs text-gray-300 overflow-auto">
                <div className="text-blue-400"># Validation Report for Refactored API Code</div>
                <div className="mt-2 text-blue-400">## Key Improvements</div>
                <div className="mt-2">
                  <div className="text-amber-400">1. **Reversed Authentication Flow**</div>
                  <div className="ml-4 text-gray-400">- Now attempts to access the main app page directly first</div>
                  <div className="ml-4 text-gray-400">- Only falls back to login page if direct access fails</div>
                </div>
                <div className="mt-2">
                  <div className="text-amber-400">2. **Robust Session Detection**</div>
                  <div className="ml-4 text-gray-400">- Uses URL patterns to detect authentication state</div>
                </div>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between p-3 border-t border-gray-700 bg-gray-800/30">
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-gray-400">
                <SkipBack className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
                className="h-7 w-7 p-0 text-gray-400"
              >
                {isPlaying ? <Pause className="h-3 w-3" /> : <Play className="h-3 w-3" />}
              </Button>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-gray-400">
                <SkipForward className="h-3 w-3" />
              </Button>
            </div>

            <div className="flex-1 mx-4">
              <div className="w-full bg-gray-800 rounded-full h-1.5">
                <div className="bg-blue-500 h-1.5 rounded-full transition-all duration-300" style={{ width: '65%' }}></div>
              </div>
            </div>

            <div className="text-xs text-gray-400">4 / 4</div>
          </div>
        </>
      )}
    </div>
  );
};

// Enhanced message content renderer
const MessageContentRenderer: React.FC<{ message: ChatMessage; isDarkMode?: boolean }> = ({ message, isDarkMode = true }) => {
  const renderContent = () => {
    switch (message.content_type) {
      case 'plan':
        return <PlanRenderer planData={message.plan_data} />;
      case 'code':
        return <CodeRenderer codeData={message.code_data} />;
      case 'file_attachment':
        return <FileAttachmentRenderer fileData={message.file_data} />;
      case 'completion':
        return <CompletionRenderer completionData={message.completion_data} />;
      case 'text':
      default:
        return (
          <div className={`${isDarkMode ? 'text-gray-200' : 'text-gray-800'} text-sm leading-relaxed whitespace-pre-wrap transition-colors duration-200`}>
            {message.content}
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      {renderContent()}
      {message.content_type !== 'file_attachment' && message.file_data && (
        <FileAttachmentRenderer fileData={message.file_data} />
      )}
    </div>
  );
};

export default function ManusChatMonitor() {
  const [message, setMessage] = useState('');
  const [profileName, setProfileName] = useState('');
  const [isHeadless, setIsHeadless] = useState(true);
  const [showComputer, setShowComputer] = useState(false);
  const [isComputerMinimized, setIsComputerMinimized] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Utility functions
  const playNotificationSound = useCallback(() => {
    if (soundEnabled) {
      const audio = new Audio('/notification.mp3');
      audio.volume = 0.3;
      audio.play().catch(() => {
        // Ignore audio play errors
      });
    }
  }, [soundEnabled]);

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // Could add toast notification here
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  }, []);

  const detectViewMode = useCallback(() => {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }, []);

  // Responsive design effect
  useEffect(() => {
    const handleResize = () => {
      setViewMode(detectViewMode());
    };

    handleResize(); // Set initial view mode
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [detectViewMode]);

  // Use WebSocket context
  const {
    isConnected,
    status: wsContextStatus,
    formattedElapsedTime,
    connect,
    sendMessage,
    chatHistory,
    monitoringActive,
  } = useWebSocket();

  // Enhanced demo messages
  const addDemoMessages = () => {
    const demoMessages: ChatMessage[] = [
      {
        id: Date.now() + 1,
        content: "I'll help you translate the Vietnamese text to English. Here's the translation:",
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'text',
        status: 'sent'
      },
      {
        id: Date.now() + 2,
        content: 'Translate to English: Continue fixing feedback tasks',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'plan',
        plan_data: {
          title: 'Translate to English: Continue fixing feedback tasks',
          steps: [
            {
              title: 'Read and analyze the API code.',
              description: 'Reading and analyzing the API code file.',
              status: 'completed',
              files: [
                { name: 'upload/pasted_content.txt', action: 'reading' },
                { name: 'upload/pasted_content_2.txt', action: 'reading' }
              ]
            },
            {
              title: 'Identify required changes for the workflow.',
              description: 'Identifying required changes for workflow alignment.',
              status: 'completed'
            },
            {
              title: 'Modify the API code to match the test workflow.',
              description: 'Modifying the API code to match the test code workflow.',
              status: 'in_progress',
              files: [
                { name: 'analysis.md', action: 'creating' },
                { name: 'modified_api_code.py', action: 'creating' }
              ]
            },
            {
              title: 'Validate the modified API code.',
              description: 'Validating the modified API code.',
              status: 'pending',
              files: [
                { name: 'validation_notes.md', action: 'creating' }
              ]
            },
            {
              title: 'Report and send the modified code and analysis documents to the user.',
              description: 'Reporting and sending modified code and analysis documents to the user.',
              status: 'pending'
            }
          ]
        },
        status: 'sent'
      },
      {
        id: Date.now() + 3,
        content: 'Cách sử dụng mới:',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'text',
        status: 'sent'
      },
      {
        id: Date.now() + 4,
        content: 'Đối với profile đã được xác thực, hãy sử dụng tham số `skip_login_check`:',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'code',
        code_data: {
          language: 'bash',
          description: 'API call example for authenticated profiles',
          code: `curl -X POST "http://localhost:8800/manus-chat-profile" \\
  -H "Content-Type: application/json" \\
  -d '{
    "chat_content": "Xin chào từ API test curl",
    "profile_name": "my_profile",
    "headless": false,
    "wait_time": 3,
    "skip_login_check": true
  }'`,
          filename: 'api_test.sh'
        },
        status: 'sent'
      },
      {
        id: Date.now() + 5,
        content: 'Tôi đã đính kèm hai tệp:',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'text',
        status: 'sent'
      },
      {
        id: Date.now() + 6,
        content: '',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'file_attachment',
        file_data: {
          filename: 'refactored_api_code.py',
          size: '35.86 KB',
          type: 'code',
          description: 'Mã API đã được cải tiến',
          url: '#'
        },
        status: 'sent'
      },
      {
        id: Date.now() + 7,
        content: '',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'file_attachment',
        file_data: {
          filename: 'validation_report_refactored.md',
          size: '2.84 KB',
          type: 'document',
          description: 'Báo cáo chi tiết về các thay đổi và kiểm tra xác nhận',
          url: '#'
        },
        status: 'sent'
      },
      {
        id: Date.now() + 8,
        content: '',
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        content_type: 'completion',
        completion_data: {
          title: 'Manus has completed the task',
          description: 'All deliverables sent to user and task complete.',
          deliverables: [
            { name: 'refactored_api_code.py', type: 'Code', size: '35.86 KB' },
            { name: 'validation_report_refactored.md', type: 'Text', size: '2.84 KB' }
          ]
        },
        status: 'sent'
      }
    ];

    setMessages(prev => [...prev, ...demoMessages]);
    setShowComputer(true);
  };

  // Function to scroll to the bottom of the chat
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Effect to scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Effect to update messages when chatHistory changes
  useEffect(() => {
    if (chatHistory && chatHistory.length > 0) {
      const formattedMessages = chatHistory
        .filter((msg): msg is Required<Pick<typeof msg, 'content'>> & typeof msg =>
          msg.content != null
        )
        .map((msg, index) => ({
          ...msg,
          id: msg.id ?? `${msg.timestamp ?? Date.now()}_${index}_${msg.content.slice(0, 10).replace(/\s/g, '') || 'empty'}`,
          status: 'sent' as const,
          content: msg.content,
        }));

      setMessages(prevMessages => {
        if (prevMessages.length === 0) {
          return formattedMessages;
        }

        const existingIds = new Set(prevMessages.map(msg => msg.id));
        const newMessages = formattedMessages.filter(msg => !existingIds.has(msg.id));

        if (newMessages.length > 0) {
          return [...prevMessages, ...newMessages];
        }

        return prevMessages;
      });

      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [chatHistory]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim()) return;

    setMessage('');

    if (!isConnected) {
      connect();
      const connectionTimeout = setTimeout(() => {
        // Handle connection timeout
      }, 5000);

      const checkInterval = setInterval(() => {
        if (isConnected) {
          clearTimeout(connectionTimeout);
          clearInterval(checkInterval);
          sendMessage(message, true, profileName);
        }
      }, 100);

      setTimeout(() => {
        clearInterval(checkInterval);
      }, 5000);
    } else {
      sendMessage(message, true, profileName);
    }
  };

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-950 text-gray-100' : 'bg-white text-gray-900'} flex flex-col transition-colors duration-300`}>
      {/* Enhanced Header */}
      <div className={`border-b ${isDarkMode ? 'border-gray-800 bg-gray-900/80' : 'border-gray-200 bg-white/80'} backdrop-blur-sm px-4 sm:px-6 py-4 sticky top-0 z-10`}>
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <div className="flex items-center gap-2 sm:gap-4">
            <ManusLogo size={viewMode === 'mobile' ? 24 : 28} className={isDarkMode ? "text-gray-100" : "text-gray-900"} />
            <h1 className={`text-lg sm:text-xl font-medium ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>manus</h1>
            {viewMode !== 'mobile' && (
              <>
                <Separator orientation="vertical" className={`h-6 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} />
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-emerald-500' : 'bg-red-500'}`}></div>
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {isConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                {monitoringActive && (
                  <>
                    <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse"></div>
                    <span className="text-sm text-blue-400">Monitoring</span>
                  </>
                )}
              </>
            )}
          </div>
          <div className="flex items-center gap-2 sm:gap-4">
            {viewMode !== 'mobile' && (
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {new Date().toLocaleDateString('en-US', { weekday: 'long' })}
              </div>
            )}

            {/* Theme Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsDarkMode(!isDarkMode)}
              className={`${isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-600 hover:text-gray-900'} h-8 w-8 p-0`}
            >
              {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </Button>

            {/* Sound Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSoundEnabled(!soundEnabled)}
              className={`${isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-600 hover:text-gray-900'} h-8 w-8 p-0`}
            >
              {soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            </Button>

            {/* View Mode Indicator */}
            {viewMode !== 'desktop' && (
              <div className={`flex items-center gap-1 px-2 py-1 rounded-md ${isDarkMode ? 'bg-gray-800 text-gray-400' : 'bg-gray-100 text-gray-600'}`}>
                {viewMode === 'mobile' ? <Smartphone className="h-3 w-3" /> : <Tablet className="h-3 w-3" />}
                <span className="text-xs capitalize">{viewMode}</span>
              </div>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className={`${isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-600 hover:text-gray-900'} h-8 w-8 p-0`}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Settings Panel */}
      {showSettings && (
        <div className={`border-b ${isDarkMode ? 'border-gray-800 bg-gray-900/50' : 'border-gray-200 bg-gray-50/50'} px-4 sm:px-6 py-4`}>
          <div className="max-w-6xl mx-auto">
            <div className={`${viewMode === 'mobile' ? 'space-y-4' : 'flex items-center gap-6'} text-sm`}>
              <div className="flex items-center gap-2">
                <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} ${viewMode === 'mobile' ? 'w-20' : 'w-16'}`}>Profile:</span>
                <Input
                  value={profileName}
                  onChange={(e) => setProfileName(e.target.value)}
                  placeholder="Enter profile name"
                  className={`h-8 ${viewMode === 'mobile' ? 'flex-1' : 'w-40'} ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-300'} text-sm`}
                />
              </div>

              <div className="flex items-center gap-2">
                <Switch
                  checked={isHeadless}
                  onCheckedChange={setIsHeadless}
                  size="sm"
                />
                <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Headless mode</span>
              </div>

              {viewMode !== 'mobile' && (
                <Separator orientation="vertical" className={`h-4 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} />
              )}

              <div className={`flex items-center gap-2 ${viewMode === 'mobile' ? 'justify-center' : ''}`}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addDemoMessages}
                  className={`h-8 text-xs ${isDarkMode ? 'border-gray-700 hover:bg-gray-800' : 'border-gray-300 hover:bg-gray-100'}`}
                >
                  <Zap className="h-3 w-3 mr-1" />
                  Demo
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowComputer(true)}
                  className={`h-8 text-xs ${isDarkMode ? 'border-gray-700 hover:bg-gray-800' : 'border-gray-300 hover:bg-gray-100'}`}
                >
                  <Monitor className="h-3 w-3 mr-1" />
                  Computer
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col max-w-6xl mx-auto w-full">
        {/* Chat Messages */}
        <ScrollArea className={`flex-1 px-4 sm:px-6 ${viewMode === 'mobile' ? 'px-3' : ''}`}>
          <div className={`py-6 sm:py-8 space-y-6 sm:space-y-8`}>
            {messages.length === 0 ? (
              <div className={`flex flex-col items-center justify-center ${viewMode === 'mobile' ? 'h-80' : 'h-96'} text-center px-4`}>
                <div className="relative mb-6">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl"></div>
                  <div className="relative">
                    <ManusLogo size={viewMode === 'mobile' ? 48 : 64} className={isDarkMode ? "text-gray-400" : "text-gray-500"} />
                  </div>
                </div>
                <h2 className={`${viewMode === 'mobile' ? 'text-xl' : 'text-2xl'} font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                  Ready to help
                </h2>
                <p className={`${isDarkMode ? 'text-gray-500' : 'text-gray-600'} max-w-md leading-relaxed ${viewMode === 'mobile' ? 'text-sm' : ''}`}>
                  I'm Manus, your AI assistant. I can help you with coding, analysis, automation, and more.
                  What would you like to work on today?
                </p>
                <div className="flex items-center gap-2 mt-6">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-emerald-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            ) : (
              <>
                {messages.map((msg: ChatMessage, index: number) => (
                  <div key={msg.id} className="group space-y-3 sm:space-y-4 animate-in slide-in-from-bottom-4 duration-500" style={{ animationDelay: `${index * 50}ms` }}>
                    {/* Message Header */}
                    <div className="flex items-center gap-3">
                      <ManusLogo size={viewMode === 'mobile' ? 20 : 24} className={isDarkMode ? "text-gray-400" : "text-gray-500"} />
                      <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>manus</span>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                        {new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>

                      {/* Message Actions */}
                      <div className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(msg.content)}
                          className={`h-6 w-6 p-0 ${isDarkMode ? 'text-gray-500 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`h-6 w-6 p-0 ${isDarkMode ? 'text-gray-500 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`}
                        >
                          <Share className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    {/* Message Content */}
                    <div className={`${viewMode === 'mobile' ? 'ml-6' : 'ml-9'}`}>
                      <MessageContentRenderer message={msg} isDarkMode={isDarkMode} />
                    </div>
                  </div>
                ))}

                {monitoringActive && (
                  <div className="space-y-3 sm:space-y-4 animate-in slide-in-from-bottom-4 duration-500">
                    <div className="flex items-center gap-3">
                      <ManusLogo size={viewMode === 'mobile' ? 20 : 24} className={isDarkMode ? "text-gray-400" : "text-gray-500"} />
                      <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>manus</span>
                      <div className="flex items-center gap-1">
                        <Loader2 className="h-3 w-3 animate-spin text-blue-400" />
                        <span className="text-xs text-blue-400">working</span>
                      </div>
                    </div>
                    <div className={`${viewMode === 'mobile' ? 'ml-6' : 'ml-9'}`}>
                      <div className={`flex items-center gap-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        <Brain className="h-4 w-4 animate-pulse" />
                        <span className={`text-sm ${viewMode === 'mobile' ? 'text-xs' : ''}`}>
                          Analyzing your request and planning the approach...
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Enhanced Input Area */}
        <div className={`border-t ${isDarkMode ? 'border-gray-800 bg-gray-900/50' : 'border-gray-200 bg-white/50'} backdrop-blur-sm px-4 sm:px-6 py-4 sm:py-6`}>
          <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-4">
            <div className={`flex items-end gap-3 sm:gap-4 ${viewMode === 'mobile' ? 'flex-col' : ''}`}>
              <div className="flex-1 relative w-full">
                <Textarea
                  ref={textareaRef}
                  value={message}
                  onChange={(e) => {
                    setMessage(e.target.value);
                    setIsTyping(e.target.value.length > 0);
                  }}
                  placeholder="Message manus"
                  className={`${viewMode === 'mobile' ? 'min-h-[48px] max-h-[100px]' : 'min-h-[52px] max-h-[120px]'} ${isDarkMode ? 'bg-gray-800/80 border-gray-700 placeholder:text-gray-500' : 'bg-white/80 border-gray-300 placeholder:text-gray-400'} resize-none text-sm pr-12 transition-all duration-200 focus:ring-2 focus:ring-blue-500/20`}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e as any);
                    }
                  }}
                />
                <div className={`absolute bottom-3 right-3 text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                  {message.length > 0 && `${message.length} chars`}
                </div>
              </div>
              <Button
                type="submit"
                disabled={!message.trim() || (wsContextStatus?.status === 'processing')}
                className={`${viewMode === 'mobile' ? 'h-[48px] w-full' : 'h-[52px] px-6'} bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl`}
              >
                {wsContextStatus?.status === 'processing' ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    {viewMode === 'mobile' && <span className="ml-2">Send</span>}
                  </>
                )}
              </Button>
            </div>

            {/* Enhanced Status Bar */}
            <div className={`flex ${viewMode === 'mobile' ? 'flex-col gap-2' : 'items-center justify-between'} text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
              <div className={`flex items-center ${viewMode === 'mobile' ? 'gap-2' : 'gap-4'}`}>
                <span className={viewMode === 'mobile' ? 'text-xs' : ''}>
                  {viewMode === 'mobile' ? 'Enter to send' : 'Press Enter to send, Shift+Enter for new line'}
                </span>
                {wsContextStatus?.status === 'processing' && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3" />
                    <span>{formattedElapsedTime()}</span>
                  </div>
                )}
                {isTyping && (
                  <div className="flex items-center gap-1">
                    <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse"></div>
                    <span className="text-blue-400">typing</span>
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                {messages.length > 0 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setMessages([]);
                      if (soundEnabled) playNotificationSound();
                    }}
                    className={`h-6 text-xs ${isDarkMode ? 'text-gray-500 hover:text-gray-300' : 'text-gray-400 hover:text-gray-600'}`}
                  >
                    Clear chat
                  </Button>
                )}
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Enhanced Manus Computer */}
      <ManusComputer
        isVisible={showComputer}
        onToggle={() => setShowComputer(false)}
        isMinimized={isComputerMinimized}
        onMinimizeToggle={() => setIsComputerMinimized(!isComputerMinimized)}
      />
    </div>
  );
}