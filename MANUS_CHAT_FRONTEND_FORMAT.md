# Manus Chat Frontend Format Documentation

## Tổng quan

Tài liệu này mô tả cấu trúc dữ liệu trả về từ FastAPI backend cho Manus Chat feature, đư<PERSON><PERSON> thiết kế để hỗ trợ 4 loại hiển thị khác nhau trên frontend:

1. **Text** - Tin nhắn văn bản thông thường
2. **Plan** - Kế hoạch với các bướ<PERSON> thực hiện
3. **Code** - Khối mã nguồn với syntax highlighting
4. **File Attachment** - Tệp đính kèm
5. **Completion** - Thông báo hoàn thành task

## C<PERSON>u trúc cơ bản của ChatMessage

```typescript
interface ChatMessage {
  id: number;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: string; // ISO format
  content_type: 'text' | 'plan' | 'code' | 'file_attachment' | 'completion';
  status: 'sent' | 'pending' | 'error';
  
  // Specialized data based on content_type
  plan_data?: PlanData;
  code_data?: CodeData;
  file_data?: FileData;
  completion_data?: CompletionData;
  
  // Legacy fields for backward compatibility
  role: string;
  time: string;
  timestamp_str?: string;
  attachments: any[];
  metadata: any;
}
```

## Chi tiết các loại content

### 1. Text Message
```json
{
  "id": 1748169564044,
  "content": "This is a simple text message.",
  "sender": "assistant",
  "timestamp": "2025-05-25T10:39:24.044194",
  "content_type": "text",
  "status": "sent"
}
```

### 2. Plan Message
```json
{
  "content_type": "plan",
  "plan_data": {
    "title": "Task Plan Title",
    "steps": [
      {
        "title": "Step 1 Title",
        "description": "Step description",
        "status": "pending", // 'pending' | 'in_progress' | 'completed'
        "files": [
          {
            "name": "file.txt",
            "action": "reading" // 'reading' | 'creating' | 'modifying'
          }
        ]
      }
    ]
  }
}
```

### 3. Code Message
```json
{
  "content_type": "code",
  "code_data": {
    "language": "python",
    "code": "def hello():\n    print('Hello World')",
    "filename": "example.py", // optional
    "description": "Example function" // optional
  }
}
```

### 4. File Attachment
```json
{
  "content_type": "file_attachment",
  "file_data": {
    "filename": "report.pdf",
    "size": "2.5 MB",
    "type": "document", // 'code' | 'document' | 'image' | 'other'
    "description": "Analysis report",
    "url": "#" // Download URL
  }
}
```

### 5. Completion Message
```json
{
  "content_type": "completion",
  "completion_data": {
    "title": "Manus has completed the task",
    "description": "Task completion details",
    "deliverables": [
      {
        "name": "output.py",
        "type": "Code",
        "size": "35.86 KB"
      }
    ]
  }
}
```

## Automatic Content Detection

Backend tự động phát hiện loại content dựa trên:

### Text Detection
- Default cho tất cả content không match các pattern khác

### Plan Detection
- Chứa các từ khóa: "step 1:", "1.", "2.", "plan:", "analyze", "identify", "modify"
- Có ít nhất 2 plan indicators

### Code Detection
- Chứa code blocks: "```"
- Chứa code keywords: "def ", "function ", "curl -x", "import "

### File Attachment Detection
- Chứa file extensions: ".py", ".js", ".md", ".pdf"
- Chứa size indicators: "KB", "MB", "bytes"

### Completion Detection
- Chứa completion phrases: "manus has completed", "task complete", "deliverables sent"

## API Endpoints

### Test Frontend Format
```bash
GET /test-frontend-format
```

Trả về demo messages với tất cả các loại content để test frontend.

### WebSocket Chat
```bash
WS /ws/chat/{session_id}
```

Nhận và gửi messages theo real-time với format đã được xử lý.

## Usage trong Frontend

```typescript
// Process incoming message
const processMessage = (message: ChatMessage) => {
  switch (message.content_type) {
    case 'text':
      return renderTextMessage(message);
    case 'plan':
      return renderPlanMessage(message.plan_data);
    case 'code':
      return renderCodeMessage(message.code_data);
    case 'file_attachment':
      return renderFileMessage(message.file_data);
    case 'completion':
      return renderCompletionMessage(message.completion_data);
    default:
      return renderTextMessage(message);
  }
};
```

## Implementation Details

### Backend Classes
- `ChatMessage`: Main message class với auto-detection
- `PlanData`, `CodeData`, `FileData`, `CompletionData`: Specialized data structures
- `process_messages_for_frontend()`: Helper function để convert messages

### Key Features
- **Auto-detection**: Tự động phát hiện content type từ text
- **Backward compatibility**: Giữ nguyên legacy fields
- **Extensible**: Dễ dàng thêm content types mới
- **Type-safe**: Structured data cho từng loại content

## Testing

Sử dụng endpoint `/test-frontend-format` để test:

```bash
curl -X GET "http://localhost:8000/test-frontend-format"
```

File test result: `frontend_test_result.json`

## Notes

- Tất cả timestamps đều ở format ISO 8601
- IDs được generate từ timestamp + random component
- Content detection có thể được override bằng cách truyền `content_type` explicitly
- File URLs hiện tại là placeholder "#", cần implement download logic
