#!/usr/bin/env python3
"""
Test script để kiểm tra frontend format cho <PERSON>us <PERSON>.
"""

import sys
import os
import json
from datetime import datetime

# Add the fastapi app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'fastapi', 'app'))

try:
    from manus_chat import (
        ChatMessage, 
        PlanData, 
        PlanStep, 
        CodeData, 
        FileData, 
        CompletionData,
        process_messages_for_frontend,
        create_demo_messages
    )
    print("✅ Successfully imported manus_chat modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_content_type_detection():
    """Test automatic content type detection."""
    print("\n=== Testing Content Type Detection ===")
    
    test_cases = [
        {
            "content": "I'll help you translate the Vietnamese text to English.",
            "expected": "text"
        },
        {
            "content": """Here's my plan:
1. Read and analyze the API code
2. Identify required changes
3. Modify the code
4. Validate the changes""",
            "expected": "plan"
        },
        {
            "content": """```bash
curl -X POST "http://localhost:8800/api" \\
  -H "Content-Type: application/json"
```""",
            "expected": "code"
        },
        {
            "content": "refactored_api_code.py (35.86 KB) - Updated API code",
            "expected": "file_attachment"
        },
        {
            "content": "Manus has completed the current task. All deliverables sent.",
            "expected": "completion"
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        msg = ChatMessage.create_frontend_message(
            content=test_case["content"],
            role="assistant"
        )
        
        detected = msg.content_type
        expected = test_case["expected"]
        
        status = "✅" if detected == expected else "❌"
        print(f"{status} Test {i+1}: Expected '{expected}', got '{detected}'")
        print(f"   Content: {test_case['content'][:50]}...")
        
        if detected != expected:
            print(f"   ⚠️  Detection failed!")

def test_plan_extraction():
    """Test plan data extraction."""
    print("\n=== Testing Plan Data Extraction ===")
    
    plan_content = """Translate to English: Continue fixing feedback tasks

1. Read and analyze the API code.
   Reading and analyzing the API code file.
   Files: upload/pasted_content.txt

2. Identify required changes for the workflow.
   Identifying required changes for workflow alignment.

3. Modify the API code to match the test workflow.
   Modifying the API code to match the test code workflow.
   Files: analysis.md, modified_api_code.py"""
    
    msg = ChatMessage.create_frontend_message(
        content=plan_content,
        role="assistant"
    )
    
    print(f"Content Type: {msg.content_type}")
    if msg.plan_data:
        print(f"Plan Title: {msg.plan_data.title}")
        print(f"Number of Steps: {len(msg.plan_data.steps)}")
        for i, step in enumerate(msg.plan_data.steps):
            print(f"  Step {i+1}: {step.title}")
            print(f"    Description: {step.description[:50]}...")
    else:
        print("❌ No plan data extracted")

def test_code_extraction():
    """Test code data extraction."""
    print("\n=== Testing Code Data Extraction ===")
    
    code_content = """Here's the API call example:

```bash
curl -X POST "http://localhost:8800/manus-chat-profile" \\
  -H "Content-Type: application/json" \\
  -d '{
    "chat_content": "Hello from API",
    "profile_name": "my_profile"
  }'
```"""
    
    msg = ChatMessage.create_frontend_message(
        content=code_content,
        role="assistant"
    )
    
    print(f"Content Type: {msg.content_type}")
    if msg.code_data:
        print(f"Language: {msg.code_data.language}")
        print(f"Code Length: {len(msg.code_data.code)} chars")
        print(f"Description: {msg.code_data.description}")
        print(f"Code Preview: {msg.code_data.code[:100]}...")
    else:
        print("❌ No code data extracted")

def test_frontend_format():
    """Test complete frontend format."""
    print("\n=== Testing Complete Frontend Format ===")
    
    # Create demo messages
    demo_messages = create_demo_messages()
    
    print(f"Created {len(demo_messages)} demo messages")
    
    # Test each message type
    for i, msg in enumerate(demo_messages):
        print(f"\nMessage {i+1}:")
        print(f"  ID: {msg['id']}")
        print(f"  Content Type: {msg['content_type']}")
        print(f"  Sender: {msg['sender']}")
        print(f"  Status: {msg['status']}")
        print(f"  Timestamp: {msg['timestamp']}")
        
        # Check specialized data
        if msg['content_type'] == 'plan' and 'plan_data' in msg:
            plan_data = msg['plan_data']
            print(f"  ✅ Plan Data: {plan_data['title']} ({len(plan_data['steps'])} steps)")
        elif msg['content_type'] == 'code' and 'code_data' in msg:
            code_data = msg['code_data']
            print(f"  ✅ Code Data: {code_data['language']} ({len(code_data['code'])} chars)")
        elif msg['content_type'] == 'file_attachment' and 'file_data' in msg:
            file_data = msg['file_data']
            print(f"  ✅ File Data: {file_data['filename']} ({file_data['size']})")
        elif msg['content_type'] == 'completion' and 'completion_data' in msg:
            completion_data = msg['completion_data']
            print(f"  ✅ Completion Data: {completion_data['title']}")
        else:
            print(f"  📝 Text Message: {msg['content'][:50]}...")

def save_demo_json():
    """Save demo messages to JSON file for frontend testing."""
    print("\n=== Saving Demo JSON ===")
    
    demo_messages = create_demo_messages()
    
    output_file = "demo_messages.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(demo_messages, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Saved {len(demo_messages)} demo messages to {output_file}")
    print(f"📁 File size: {os.path.getsize(output_file)} bytes")

def main():
    """Run all tests."""
    print("🚀 Starting Frontend Format Tests")
    print("=" * 50)
    
    try:
        test_content_type_detection()
        test_plan_extraction()
        test_code_extraction()
        test_frontend_format()
        save_demo_json()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
